import useReportStore from "features/Reports/store/report";
import { useAutoSave } from "hooks";
import { useEffect, useState } from "react";
import HeaderBar from "./HeaderBar";
import SectionBlock from "./SectionBlock";
import "./styles.scss";

interface ReportBuilderProps {
  onSaveReport: any;
  children?: React.ReactNode;
  saveReportSilenty: any;
}

const ReportBuilder = ({
  onSaveReport,
  children = <></>,
  saveReportSilenty,
}: ReportBuilderProps) => {
  const reportInfo: any = useReportStore((state) => state.reportInfo);
  const { title, sections } = reportInfo || {};

  // Local state to track which section should be autofocused
  const [autofocusIndex, setAutofocusIndex] = useState<number | null>(null);

  const sortedSections = sections?.sort(
    (a: any, b: any) => a.position - b.position
  );

  useAutoSave({
    onAutoSave: saveReportSilenty,
    autoSaveArgs: { saveSilently: true },
  });

  // Effect to detect when a new section is added and set autofocus
  useEffect(() => {
    if (sections && sections.length > 0) {
      // Find the section that was just added (typically the one with the highest position)
      const latestSection = sections.reduce((latest: any, current: any) => {
        return current.position > latest.position ? current : latest;
      });

      // If we detect a new section was added, set autofocus to its index
      if (latestSection && latestSection.title === "New Section" && latestSection.content?.content === "") {
        const newSectionIndex = sections.findIndex((section: any) => section === latestSection);
        if (newSectionIndex !== -1) {
          setAutofocusIndex(newSectionIndex);
          // Clear autofocus after a short delay to prevent re-focusing
          setTimeout(() => setAutofocusIndex(null), 100);
        }
      }
    }
  }, [sections]);

  return (
    <div className="report-builder-section bg-white h-100 w-100 rounded p-3 overflow-auto">
      <HeaderBar title={title} onSaveReport={onSaveReport} />
      {sortedSections.map((block: any, idx: number) => (
        <SectionBlock
          key={idx}
          {...block}
          index={idx}
          saveReportSilenty={saveReportSilenty}
          shouldAutofocus={autofocusIndex === idx}
        />
      ))}
      {children}
    </div>
  );
};

export default ReportBuilder;
