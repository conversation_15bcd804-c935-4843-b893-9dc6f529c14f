import { RiSurveyLine } from "@remixicon/react";
import ActionButton from "features/Reports/components/ActionButton";
import useReportStore from "features/Reports/store/report";
import { useState } from "react";
import ComplianceModal from "../ComplianceModal";
import { useTiptapConverter } from "../hooks/useTiptapConverter";

const CheckComplianceButton = () => {
  const [showComplianceModal, setShowComplianceModal] = useState(false);

  const reportInfo = useReportStore((state) => state.reportInfo);
  const { convertSections } = useTiptapConverter();

  const onComplianceClick = () => {
    setShowComplianceModal(true);
  };

  return (
    <>
      <ActionButton
        icon={RiSurveyLine}
        onClick={onComplianceClick}
        title="Check Suitability Compliance"
      />
      {showComplianceModal && (
        <ComplianceModal
          show={showComplianceModal}
          onClose={() => setShowComplianceModal(false)}
          payload={{
            title: reportInfo.title,
            sections: convertSections(reportInfo.sections, "html"),
          }}
        />
      )}
    </>
  );
};

export default CheckComplianceButton;
