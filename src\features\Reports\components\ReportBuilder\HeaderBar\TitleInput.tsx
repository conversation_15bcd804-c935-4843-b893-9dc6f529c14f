import { RiCheckboxCircleLine, RiQuillPenFill } from "@remixicon/react";
import { setReportTitle } from "features/Reports/store";
import { useEffect, useRef, useState } from "react";

const TitleInput = ({ title }: { title: string }) => {
  const titleRef = useRef<HTMLInputElement>(null);
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [localTitle, setLocalTitle] = useState(title);

  useEffect(() => {
    setLocalTitle(title);
    if (titleRef.current) {
      titleRef.current.value = title || "";
    }
  }, [title]);

  const handleEditTitle = () => {
    setIsEditingTitle(true);
    setTimeout(() => titleRef.current?.focus(), 0); // Focus input after re-render
  };

  const handleTitleSave = () => {
    if (titleRef.current) {
      const newTitle = titleRef.current.value.trim();
      if (newTitle && newTitle !== title) {
        setReportTitle(newTitle);
        setLocalTitle(newTitle);
      } else if (!newTitle) {
        setLocalTitle(title);
      }
      setIsEditingTitle(false);
    }
  };

  const handleTitleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleTitleSave();
    } else if (e.key === "Escape") {
      setLocalTitle(title);
      setIsEditingTitle(false);
    }
  };

  const handleTitleBlur = () => {
    handleTitleSave();
  };
  return (
    <div
      className={`header-title rounded w-100 position-relative ${isEditingTitle ? "border-1 border-brown bg-white" : ""}`}
    >
      <input
        type="text"
        className="report-title-input w-100 text-center fw-bold"
        placeholder="Enter report title..."
        readOnly={!isEditingTitle}
        ref={titleRef}
        defaultValue={localTitle}
        onKeyDown={handleTitleKeyDown}
        onBlur={handleTitleBlur}
      />
      {isEditingTitle ? (
        <button className="edit-title-btn">
          <RiCheckboxCircleLine color="#ad986f" />
        </button>
      ) : (
        <button className="edit-title-btn" onClick={handleEditTitle}>
          <RiQuillPenFill />
        </button>
      )}
    </div>
  );
};

export default TitleInput;
