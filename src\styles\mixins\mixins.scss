@use "../variables" as *;
@forward "./button";
@forward "./forms";

@mixin page-reset {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

@mixin body-formatting {
  box-sizing: border-box !important;
  font-family: "Urbanist", "Helvetica Neue", Aria<PERSON>, sans-serif !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  line-height: 1.6 !important;
  background-color: #f9f9f9 !important;
  color: #0d3149 !important;

  @media only screen and (min-width: 992px) {
    min-height: 100vh;
  }

  &::-webkit-scrollbar {
    width: 10px;

    &-track {
      background: $scrollbar-track-color;
    }

    &-thumb {
      background: $scrollbar-thumb-color;

      &:hover {
        background: $scrollbar-thumb-hover-color;
      }
    }
  }
}

@mixin link-bottomLine-hover {
  &::before {
    content: " ";
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: #ad986f;
    left: 0;
    right: 0;
    bottom: 0;
    transform-origin: right;
    transform: scaleX(0);
    transition: transform 0.4s ease-in-out;
  }

  &:hover::before {
    transform-origin: left;
    transform: scaleX(1);
  }

  &.active::before {
    transform: none !important;
    transform-origin: inherit !important;
  }
}

@mixin slim-scrollbar($thumb-color: #eaecf0, $track-color: transparent) {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px $track-color;
    border-radius: 12px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 6px $thumb-color;
    background-color: $thumb-color;
  }
}

@mixin header-nav-link-base {
  width: 45px;
  height: 45px;
  border-radius: 5px;
  vertical-align: middle;
  padding: 10px;
}

@mixin uploaded-file-container {
  gap: 15px;
  min-width: 180px;
  max-width: 335px;
  line-height: 1;
  border: 1px solid #0d3149;
  border-radius: 8px;
  padding: 10px;

  @media only screen and (max-width: 1599px) {
    max-width: 60%;
  }

  @media only screen and (max-width: 1199px) {
    max-width: 90%;
  }

  @media only screen and (max-width: 991px) {
    max-width: 70%;
  }

  @media only screen and (max-width: 576px) {
    max-width: 80%;
  }

  hr {
    border-top: none;
    border-right: 1px solid #0d3149;
  }

  img {
    width: 40px;
    height: 50px;
  }

  &.type-image {
    min-width: auto;
    min-height: auto;

    img {
      width: 70px;
      height: 70px;
    }
  }

  &:hover {
    .remove-btn {
      opacity: 1;
      visibility: visible;
    }
  }
}

@mixin user-chat-message {
  &-message {
    &-text {
      border-radius: 8px;
      box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.12);
      padding: 12px 16px;
      min-width: 150px;
      background: #f9f9f9;
      font-weight: 500;
      width: fit-content;
      margin-left: auto;
    }

    &-image {
      .uploaded-image {
        background-size: cover;
        background-position: center;
        width: 300px;
        height: 200px;
        border-radius: 8px;
        border: 2px solid #0d3149;

        @media only screen and (max-width: 1599px) {
          width: 250px;
          height: 170px;
        }

        @media only screen and (max-width: 1199px) {
          width: 220px;
          height: 150px;
        }

        @media only screen and (max-width: 576px) {
          width: 180px;
          height: 120px;
        }

        &.loading {
          opacity: 0.5;
        }
      }

      .image-loader {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 2;
      }

      .remove-btn {
        position: absolute;
        top: 5px;
        right: 5px;
        background-color: #ffffff;
        border: 1px solid #ddd;
        border-radius: 50%;
        width: 35px;
        height: 35px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease-in-out;

        @media only screen and (min-width: 992px) {
          opacity: 0;
        }
      }

      &:hover {
        .remove-btn {
          opacity: 1;
        }
      }
    }

    &-file {
      @include uploaded-file-container;
    }
  }
}

@mixin breadcrumb-commom-style {
  .breadcrumb-wrapper {
    .page-details {
      gap: 20px;

      @media only screen and (max-width: 576px) {
        gap: 10px;
      }

      &-img {
        width: 50px;
        height: 50px;

        @media only screen and (max-width: 576px) {
          width: 40px;
          height: 40px;
        }
      }

      &-page-name {
        h3 {
          font-size: 22px;
          line-height: 1;

          @media only screen and (max-width: 576px) {
            font-size: 16px;
          }
        }

        p {
          font-size: 18px;
          color: rgba(26, 26, 26, 0.5);
          font-weight: 500;
          line-height: 1;

          @media only screen and (max-width: 576px) {
            font-size: 13px;
            line-height: normal;
          }
        }
      }
    }

    button {
      width: 50px;
      height: 50px;
      box-shadow: none !important;

      @media only screen and (max-width: 576px) {
        width: 30px;
        height: 30px;

        svg {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}

@mixin universal-heading-subheading {
  &-heading {
    font-size: 40px;
    font-weight: 700;

    @media only screen and (max-width: 576px) {
      font-size: 28px;
    }
  }

  &-description {
    font-size: 18px;

    @media only screen and (max-width: 576px) {
      font-size: 16px;
    }
  }
}

@mixin notification-dot($top, $right) {
  .notification-dot {
    width: 12px;
    height: 12px;
    top: $top;
    right: $right;
    border: 1px solid #f9f9f9;
  }
}

@mixin table-td-text-overflow {
  td {
    line-height: 1.4;
    max-height: none;
    white-space: normal;
    word-break: break-word;
    overflow-wrap: break-word;
    padding: 12px 16px;
  }
}

@mixin table-body-fix-height {
  tbody {
    min-height: calc(100vh - 630px);
    display: block;
  }

  thead,
  tbody tr {
    display: table;
    table-layout: fixed;
    width: 100%;
  }
}

@mixin internal-sm-card {
  padding: 60px 60px;
  border-radius: 20px;
  border: 5px solid #f7f7f7;
  box-shadow:
    0 65px 47px #1a1a1a0a,
    0 100px 80px #1a1a1a0d;

  @media only screen and (max-width: 767px) {
    padding: 0px;
    box-shadow: none;
  }
}
