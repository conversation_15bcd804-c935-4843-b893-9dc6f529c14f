export const getFormattedDate = (date: string) => {
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
};

export const fromTimestampToDate = (date: string) => {
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  const d = new Date(date);

  const month = months[d.getMonth()];
  const day = d.getDate();
  const year = d.getFullYear();

  let hours = d.getHours();
  const minutes = d.getMinutes();
  const ampm = hours >= 12 ? "PM" : "AM";
  hours = hours % 12;
  hours = hours ? hours : 12;

  const strMinutes = minutes < 10 ? "0" + minutes : minutes;

  return `${month} ${day} ${year}, ${hours}:${strMinutes} ${ampm}`;
};

export const getExpiryDuration = (exp_month: number, exp_year: number) => {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1;

  let yearDiff = exp_year - currentYear;
  let monthDiff = exp_month - currentMonth;

  if (monthDiff < 0) {
    yearDiff -= 1;
    monthDiff += 12;
  }

  if (yearDiff < 0 || (yearDiff === 0 && monthDiff === 0)) {
    return "Card has expired";
  }

  const yearsText =
    yearDiff > 0 ? `${yearDiff} year${yearDiff > 1 ? "s" : ""}` : "";
  const monthsText =
    monthDiff > 0 ? `${monthDiff} month${monthDiff > 1 ? "s" : ""}` : "";

  const durationText = [yearsText, monthsText].filter(Boolean).join(" and ");

  return `ending in ${durationText}`;
};

export const getStripeFormateDate = (inputDate: any) => {
  const date = new Date(inputDate);

  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  const day = date.getUTCDate();
  const month = monthNames[date.getUTCMonth()];
  const year = date.getUTCFullYear();

  return `${day} ${month} ${year}`;
};

export const formatUnixTimestamp = (timestamp: any) => {
  const date = new Date(timestamp * 1000);

  const options: any = { day: "2-digit", month: "short", year: "numeric" };
  return date.toLocaleDateString("en-GB", options);
};

export const convertTimestampToFormat = (date: any) => {
  if (!date) return;
  return new Date(date).toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });
};

export const getTimezone = () =>
  Intl.DateTimeFormat().resolvedOptions().timeZone;
