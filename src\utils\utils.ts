import auth0, { Auth0DecodedHash, Auth0ParseHashError } from "auth0-js";
import { AUTH0_INFO, CHAT_UPLOAD_MIME_TYPES, LOGIN_TYPE } from "globals";
import toast from "react-hot-toast";
import { getAllowedFileTypes } from "stores";

export const processHash = (
  hash: string,
  setLoading: any,
  fetchUserProfile: any
) => {
  const { AUTH0_DOMAIN, AUTH0_CLIENT_ID, AUTH0_SCOPE } = AUTH0_INFO ?? {};
  setLoading(true);
  const webAuth = new auth0.WebAuth({
    domain: AUTH0_DOMAIN,
    clientID: AUTH0_CLIENT_ID,
    scope: AUTH0_SCOPE,
  });

  webAuth.parseHash(
    {
      hash,
    },
    (err: Auth0ParseHashError | null, result: Auth0DecodedHash | null) => {
      if (err) {
        console.log(err);
        window.history.replaceState(null, "", window.location.pathname);
        setLoading(false);
        return;
      }
      if (result) {
        const { accessToken } = result;
        if (accessToken) {
          fetchUserProfile(accessToken, LOGIN_TYPE.EMBEDDED);
          window.history.replaceState(null, "", window.location.pathname);
        }
      }
    }
  );
};

export const fillValues = (
  initialValues: Record<string, any>,
  sourceData: Record<string, any>
) => {
  const filledValues = { ...initialValues };

  Object.keys(initialValues).forEach((key) => {
    if (sourceData && sourceData[key] !== undefined) {
      filledValues[key] = sourceData[key];
    }
  });

  return filledValues;
};

export const generateNickName = (name: string | null) => {
  if (!name) return;

  const nameArray = name.split(" ");
  if (nameArray?.[0] && nameArray?.[1]) {
    return `${nameArray[0][0]}${nameArray[1][0]}`;
  } else {
    return nameArray[0][0];
  }
};

export const isValidFileType = (file: File) => {
  const validMimeTypes: any = [
    CHAT_UPLOAD_MIME_TYPES.JPEG,
    CHAT_UPLOAD_MIME_TYPES.PNG,
  ];
  if (validMimeTypes.includes(file.type)) {
    return true;
  } else {
    toast.error(
      "Invalid Image Type!\nPlease upload one of the following: JPEG, PNG or JPG."
    );
    return false;
  }
};

export const isValidDocType = (file: File) => {
  const validMimeTypes: any = getAllowedFileTypes()?.length
    ? getAllowedFileTypes()
    : Object.values(CHAT_UPLOAD_MIME_TYPES);
  if (validMimeTypes.includes(file.type)) {
    return true;
  }
  toast.error(
    `Invalid File Type!\nPlease upload one of the following: ${Object.keys(CHAT_UPLOAD_MIME_TYPES).join(", ")}.`
  );
  return false;
};

export const isValidPromptFileType = (file: File) => {
  const validMimeTypes: any = [
    CHAT_UPLOAD_MIME_TYPES.DOCX,
    CHAT_UPLOAD_MIME_TYPES.PDF,
  ];
  if (validMimeTypes.includes(file.type)) {
    return true;
  }
  toast.error(
    "Invalid File Type!\nPlease upload one of the following: PDF or DOCX."
  );
  return false;
};

export const isValidFileSize = (file: File) => {
  const maxSize = 20 * 1024 * 1024; // 20MB
  if (file.size === 0) {
    toast.error("Invalid Empty File!");
    return false;
  }
  if (file.size <= maxSize) {
    return true;
  }
  toast.error(
    `${file.type.startsWith("image/") ? "Image" : "File"} size should be less then 5 MB.`
  );
  return false;
};

export const fromCentsToDollar = (cents: number | undefined) => {
  if (!cents) return;
  return (cents / 100).toFixed(2);
};

export const getAppOriginURL = () => {
  return `${window.location.origin}${import.meta.env.VITE_BASE_URL?.length > 1 ? import.meta.env.VITE_BASE_URL : ""}`;
};

export const convertObjToFormData = (obj: Record<string, any>) => {
  const formData = new FormData();
  Object.keys(obj).forEach((key) => {
    if (!obj[key]) return;
    if (Array.isArray(obj[key])) {
      obj[key].forEach((item) => {
        if (item instanceof File) {
          formData.append(key, item, item.name);
        } else {
          formData.append(key, item);
        }
      });
    } else {
      formData.append(key, obj[key]);
    }
  });
  return formData;
};

export const formatNumberWithCommas = (
  num: number | string | undefined
): string => {
  if (!num) return "0";
  return num.toLocaleString("en-US");
};

export const downloadBlob = (blob: Blob, filename: string) => {
  const href = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = href;
  link.setAttribute("download", filename);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(href);
};
