@use "/src/styles/mixins/mixins.scss" as mixins;

.default-dashboard-container {
  padding: 30px;
  gap: 10px;
  overflow: hidden;
  position: relative;
  height: calc(100vh - 183px);
  border-radius: 12px;
  box-shadow:
    0px 65px 47px 0px rgba(26, 26, 26, 0.04),
    0px 100px 80px 0px rgba(26, 26, 26, 0.05);

  @media (min-height: 500px) and (max-height: 1070px) {
    overflow-x: hidden;
    overflow-y: auto;
    @include mixins.slim-scrollbar;
  }

  @media only screen and (max-width: 991px) {
    height: auto;
    padding: 25px;
  }

  @media only screen and (max-width: 767px) {
    gap: 10px;
    padding: 20px;
  }

  @media only screen and (max-width: 576px) {
    padding: 15px;
  }

  @include mixins.breadcrumb-commom-style;
}
