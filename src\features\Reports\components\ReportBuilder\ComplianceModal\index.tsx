import { useReportCheckCompliance } from "features/Reports/api";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>, Badge, Modal, Spinner } from "react-bootstrap";
import "./styles.scss";
import { RiCloseLine } from "@remixicon/react";

const ComplianceModal = ({
  show,
  onClose,
  payload,
}: {
  show: boolean;
  onClose?: () => void;
  payload: any;
}) => {
  const [hasFetched, setHasFetched] = useState(false);

  const {
    data: { data: complianceData = {} } = {},
    isFetching,
    isSuccess,
    isError,
  }: any = useReportCheckCompliance(payload);

  useEffect(() => {
    if (isSuccess && !isFetching) {
      setHasFetched(true);
    }
  }, [isSuccess, isFetching]);

  useEffect(() => {
    if (show) {
      setHasFetched(false);
    }
  }, [show, payload]);

  return (
    <Modal show={show} size="lg" centered className="compliance-modal">
      <Modal.Header className="border-0 pb-0">
        <Modal.Title className="w-100 text-center">
          <h3 className="mb-0 fw-bold">
            Suitability Report <br />
            Compliance Checker
          </h3>
          <Badge
            bg="warning"
            className="position-absolute top-0 start-0 m-3 shadow text-dark"
          >
            Beta
          </Badge>
        </Modal.Title>
        <Button
          variant="link"
          className="text-decoration-none position-absolute end-0 top-0 mt-2 me-2 text-dark"
          onClick={hasFetched || isError ? onClose : undefined}
        >
          <RiCloseLine size={24} />
        </Button>
      </Modal.Header>

      <Modal.Body>
        <div className="compliance-wrapper">
          {isFetching && !hasFetched && (
            <div className="d-flex flex-column align-items-center justify-content-center py-5">
              <Spinner animation="border" role="status" />
              <div className="mt-3 text-muted">
                Checking compliance, please wait...
              </div>
            </div>
          )}

          {isError && (
            <div className="d-flex flex-column align-items-center justify-content-center py-5">
              <div className="mt-3 text-muted">
                Error checking compliance. Please try again later.
              </div>
            </div>
          )}

          {!isFetching && hasFetched && (
            <>
              <div className="score-bar">
                <div className="bar">
                  <div
                    className="indicator"
                    style={{
                      left: `${complianceData?.overall_score}%`,
                      top: "20px",
                    }}
                  />
                </div>
                <div className="score-percent">
                  {complianceData?.overall_score}%
                </div>
                <div className="score-label">
                  {complianceData?.overall_result}
                </div>
              </div>

              <div className="mt-4">
                <table className="compliance-table">
                  <thead>
                    <tr>
                      <th>Compliance Area</th>
                      <th>Score</th>
                      <th>Status</th>
                      <th>Comments</th>
                    </tr>
                  </thead>
                  <tbody>
                    {complianceData?.compliance_summary?.map(
                      (item: any, idx: number) => (
                        <tr key={idx}>
                          <td>{item.category}</td>
                          <td>{item.score}</td>
                          <td>{item.status}</td>
                          <td>{item.comment}</td>
                        </tr>
                      )
                    )}
                  </tbody>
                </table>
              </div>
            </>
          )}
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default ComplianceModal;
