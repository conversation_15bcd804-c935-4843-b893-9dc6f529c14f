import { CustomCarousel, PromptCardSkeleton } from "components/Common";
import { IMAGE_PATH, OrgUserRole } from "globals";
import { useMediaQuery, useUpdateOrgPrompt, useUserRoles } from "hooks";
import { isMobile } from "react-device-detect";
import { getOrganisationInfo } from "stores";
import PromptCard from "./PromptCard";
import "./styles.scss";

interface PromptProps {
  isLoading: boolean;
  prompts: any[];
  allStoredPrompts: any[];
  customStyling?: any;
  onSavePrompt?: (prompt_data: any) => void;
  onResetPrompt?: (data: any) => void;
  promptConfig?: any;
  onLocalReset?: any;
  storedOrgPrompts?: any[];
  customCardClassName?: any;
}

export default function Prompts({
  isLoading,
  prompts,
  allStoredPrompts,
  customStyling,
  onSavePrompt,
  onResetPrompt,
  promptConfig,
  onLocalReset,
  storedOrgPrompts = [],
  customCardClassName = {},
}: PromptProps) {
  const isDesktop = useMediaQuery("(min-width: 1024px)");
  const organisation = getOrganisationInfo();
  const { onClickReset, onClickSubmit } = useUpdateOrgPrompt();
  const userRole = useUserRoles();

  const renderCustomPrompts = () => {
    return prompts.map((cardData: any, index: number) => (
      <PromptCard
        key={index}
        allStoredPrompts={allStoredPrompts}
        promptData={cardData}
        onSavePrompt={onSavePrompt}
        onResetPrompt={onResetPrompt}
        promptConfig={promptConfig}
        onLocalReset={onLocalReset}
        customCardClassName={customCardClassName}
      />
    ));
  };

  const renderOrgPrompts = () => {
    const orgPromptsProps = {
      onSavePrompt: (prompt_data: any) => {
        if (prompt_data?.organization_id) {
          onClickSubmit({
            organisation,
            prompt_data,
            queryKey: "custom-prompts",
          });
        }
      },
      onResetPrompt: (data: any) => {
        if (data?.promptData?.organization_id) {
          onClickReset({ ...data, organisation, queryKey: "custom-prompts" });
        }
      },
    };
    return storedOrgPrompts.map((cardData: any, index: number) => (
      <PromptCard
        key={`org-${index}`}
        allStoredPrompts={storedOrgPrompts}
        promptData={{ ...cardData, id: cardData?.prompt_id }}
        promptConfig={{
          isPromptEnabled: true,
          isPromptEditable: userRole === OrgUserRole.USER ? false : true,
          rightIcon: IMAGE_PATH.organisationIcon,
          placeholderIcon: IMAGE_PATH.organisationIcon,
        }}
        onLocalReset={onLocalReset}
        {...orgPromptsProps}
      />
    ));
  };

  const renderPrompts = () =>
    storedOrgPrompts.length
      ? renderOrgPrompts().concat(renderCustomPrompts())
      : renderCustomPrompts();

  const showScroller = isDesktop && renderPrompts().length > 4;
  return (
    <div
      className={`prompts-wrapper d-flex flex-row g-4 align-items-stretch ${isMobile ? "justify-content-between" : "justify-content-center"}`}
      style={{ ...customStyling }}
    >
      {isLoading ? (
        <>
          {Array.from({ length: 4 }).map((_, index) => (
            <PromptCardSkeleton key={index} />
          ))}
        </>
      ) : showScroller ? (
        <CustomCarousel className="gap-4">{renderPrompts()}</CustomCarousel>
      ) : (
        renderPrompts()
      )}
    </div>
  );
}
