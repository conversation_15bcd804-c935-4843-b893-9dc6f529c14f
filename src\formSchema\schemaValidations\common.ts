import * as Yup from "yup";

// Common reusable validations
export const emailValidation = Yup.string()
  .email("Invalid email address.")
  .matches(/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i, "Invalid email address.")
  .required("Email is required.");

export const passwordValidation = (fieldName: string = "password") =>
  Yup.string()
    .required(`${fieldName} is required.`)
    .test(
      "invalid-characters",
      "${path} contains invalid characters: ${invalidChars}",
      function (value) {
        if (!value) return true;

        const allowedCharSet =
          /^[A-Za-z0-9!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?`~]$/;

        const invalidChars = [...value].filter(
          (char) => !allowedCharSet.test(char),
        );

        if (invalidChars.length === 0) return true;

        return this.createError({
          message: `${fieldName} contains invalid characters: ${[...new Set(invalidChars)].join(", ")}`,
        });
      },
    )
    .matches(
      /^(?=.*[a-z])/,
      `${fieldName} must contain at least one lowercase letter.`,
    )
    .matches(/^(?=.*[A-Z])/, `${fieldName} must contain one uppercase letter.`)
    .matches(/^(?=.*\d)/, `${fieldName} must contain at least one number.`)
    .matches(
      /^(?=.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?`~])/,
      `${fieldName} must contain at least one special character.`,
    )
    .min(8, `${fieldName} must be at least 8 characters long.`)
    .max(64, `${fieldName} must be less than 64 characters.`);

export const confirmPasswordValidation = (refName: string = "password") =>
  Yup.string()
    .oneOf([Yup.ref(refName)], "Passwords must match.")
    .required("Confirm Password is required.");

export const stringRequiredValidation = (fieldName: string) =>
  Yup.string().required(`${fieldName} is required.`);
