import { useState } from "react";
import useCheckReportSection from "../hooks/useCheckReportSection";
import CheckComplianceButton from "./CheckComplianceButton";
import ExportReportButton from "./ExportReportButton";
import PreviewButton from "./PreviewButton";
import SaveReportButton from "./SaveReportButton";
import TitleInput from "./TitleInput";

interface HeaderBarProps {
  title?: string;
  onSaveReport: () => void;
}

const HeaderBar = ({ title = "", onSaveReport }: HeaderBarProps) => {
  // TEMPORARY: Toggle for JSON format (will be removed later)
  const [useJsonFormat, setUseJsonFormat] = useState(false);
  const { isGenerateSection } = useCheckReportSection();

  return (
    <>
      <div className="header d-flex gap-3">
        <TitleInput title={title} />
        <div className="header-actions d-flex gap-2">
          <CheckComplianceButton />
          <PreviewButton />
          {/* <ToggleContentFormat
            useJsonFormat={useJsonFormat}
            setUseJsonFormat={setUseJsonFormat}
          /> */}
          {isGenerateSection && <ExportReportButton />}
          <SaveReportButton
            useJsonFormat={useJsonFormat}
            onSaveReport={onSaveReport}
          />
        </div>
      </div>
      {isGenerateSection && (
        <span
          className="text-muted d-flex ps-2 fw-bold"
          style={{ fontSize: "10px" }}
        >
          *AI can make mistakes, please review this before sharing the report.
        </span>
      )}
    </>
  );
};

export default HeaderBar;
