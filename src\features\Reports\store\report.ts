import { create } from "zustand";

interface ContentStructure {
  type: string;
  content: string;
}

interface ReportBlock {
  title: string;
  content: ContentStructure | string;
  position: number;
  is_locked?: boolean;
  is_confirmed?: boolean;
}

interface PayloadInterface {
  reportInfo: {
    title: string;
    sections: ReportBlock[];
  };
}

const initialState = {
  reportInfo: {
    title: "",
    sections: [
      {
        position: 0,
        title: "Insert Title Here...",
        content: {
          type: "html",
          content: "",
        },
        is_locked: false,
      },
    ],
  },
  isDirty: false,
};

const reportStore = (set: any) => ({
  ...initialState,
  setReportInfo: (data: any) =>
    set((state: PayloadInterface) => ({
      ...state,
      reportInfo: data,
    })),
  setReportTitle: (data: string) =>
    set((state: PayloadInterface) => ({
      ...state,
      isDirty: true,
      reportInfo: { ...state.reportInfo, title: data },
    })),
  setReportBlocks: (data: ReportBlock) =>
    set((state: PayloadInterface) => ({
      ...state,
      isDirty: true,
      reportInfo: {
        ...state.reportInfo,
        sections: [...state.reportInfo.sections, data],
      },
    })),
  addReportBlockAtIndex: (data: ReportBlock, index: number) =>
    set((state: PayloadInterface) => {
      const newSections = [...state.reportInfo.sections];
      newSections.splice(index + 1, 0, {
        ...data,
        position: index + 1,
      });
      for (let i = index + 2; i < newSections.length; i++) {
        newSections[i].position = i;
      }
      return {
        ...state,
        isDirty: true,
        reportInfo: {
          ...state.reportInfo,
          sections: newSections,
        },
      };
    }),
  removeReportBlockAtIndex: (index: number) =>
    set((state: PayloadInterface) => {
      const newSections = [...state.reportInfo.sections];
      newSections.splice(index, 1);
      for (let i = index; i < newSections.length; i++) {
        newSections[i].position = i;
      }
      return {
        ...state,
        isDirty: true,
        reportInfo: {
          ...state.reportInfo,
          sections: newSections,
        },
      };
    }),
  swapReportBlocks: (index1: number, index2: number) =>
    set((state: PayloadInterface) => {
      const newSections = [...state.reportInfo.sections];
      if (
        index1 >= 0 &&
        index1 < newSections.length &&
        index2 >= 0 &&
        index2 < newSections.length
      ) {
        // Store the content and title to swap
        const block1Title = newSections[index1].title;
        const block1Content = newSections[index1].content;
        const block2Title = newSections[index2].title;
        const block2Content = newSections[index2].content;

        // Swap only the content and title, keep position tied to position
        newSections[index1] = {
          ...newSections[index1],
          title: block2Title,
          content: block2Content,
          position: index1, // Keep position tied to position
        };

        newSections[index2] = {
          ...newSections[index2],
          title: block1Title,
          content: block1Content,
          position: index2, // Keep position tied to position
        };
      }
      return {
        ...state,
        isDirty: true,
        reportInfo: {
          ...state.reportInfo,
          sections: newSections,
        },
      };
    }),
  updateReportBlockTitle: (index: number, title: string) =>
    set((state: PayloadInterface) => {
      const newSections = [...state.reportInfo.sections];
      if (index >= 0 && index < newSections.length) {
        newSections[index] = {
          ...newSections[index],
          title: title,
        };
      }
      return {
        ...state,
        isDirty: true,
        reportInfo: {
          ...state.reportInfo,
          sections: newSections,
        },
      };
    }),
  updateReportBlockContent: (index: number, content: any) =>
    set((state: PayloadInterface) => {
      const newSections = [...state.reportInfo.sections];
      if (index >= 0 && index < newSections.length) {
        newSections[index] = {
          ...newSections[index],
          content: content,
        };
      }
      return {
        ...state,
        isDirty: true,
        reportInfo: {
          ...state.reportInfo,
          sections: newSections,
        },
      };
    }),
  updateReportBlockLockState: (index: number, isLocked: boolean) =>
    set((state: PayloadInterface) => {
      const newSections = [...state.reportInfo.sections];
      if (index >= 0 && index < newSections.length) {
        newSections[index] = {
          ...newSections[index],
          is_locked: isLocked,
        };
      }
      return {
        ...state,
        isDirty: true,
        reportInfo: {
          ...state.reportInfo,
          sections: newSections,
        },
      };
    }),
  updateReportBlockConfirmState: (
    index: number | undefined,
    isConfirmed: boolean
  ) =>
    set((state: PayloadInterface) => {
      if (index === undefined) return state;
      const newSections = [...state.reportInfo.sections];
      if (index >= 0 && index < newSections.length) {
        newSections[index] = {
          ...newSections[index],
          is_confirmed: isConfirmed,
        };
      }
      return {
        ...state,
        isDirty: true,
        reportInfo: {
          ...state.reportInfo,
          sections: newSections,
        },
      };
    }),
  setIsDirty: (value: boolean) =>
    set((state: PayloadInterface & { isDirty: boolean }) => ({
      ...state,
      isDirty: value,
    })),
  resetReportState: () => set(() => initialState),
});

// const useReportStore: any = create(
//   devtools(
//     persist(reportStore, {
//       name: "report",
//     })
//   )
// );

const useReportStore = create(reportStore);

export const {
  setReportInfo,
  setReportTitle,
  setReportBlocks,
  addReportBlockAtIndex,
  removeReportBlockAtIndex,
  swapReportBlocks,
  updateReportBlockTitle,
  updateReportBlockContent,
  updateReportBlockLockState,
  updateReportBlockConfirmState,
  resetReportState,
  setIsDirty,
} = useReportStore.getState();

export default useReportStore;
