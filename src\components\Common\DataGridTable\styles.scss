@use "/src/styles/mixins/mixins.scss" as mixins;

.table-responsive {
  max-height: calc(100vh - 450px) !important;
}

.custom-table {
  tbody {
    td {
      padding: 15px 20px;

      .image-icon {
        width: 30px;
        height: 30px;
      }

      &:first-child {
        border-top-left-radius: 10px;
        border-bottom-left-radius: 10px;
      }

      &:last-child {
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
      }

      &.blank-row {
        border: none;
        padding: 0;
        margin: 0;
        height: 15px;
        border-radius: 0px;
        background-color: transparent !important;
        box-shadow: none !important;
      }

      @media only screen and (max-width: 1499px) {
        .end-row {
          padding-right: 130px;
        }
      }

      .action-btns {
        position: absolute;
        z-index: 1;
        right: -10px;
        transition: all 0.4s ease;

        @media only screen and (min-width: 992px) {
          opacity: 0;
          visibility: hidden;
        }

        .submit-btn {
          @include mixins.submit-btn;
          height: 35px;
          font-size: 12px;
          border-radius: 5px;
        }
      }

      @media only screen and (max-width: 1499px) {
        &.align-baseline {
          min-width: 400px;
        }
      }
    }

    tr {
      &.selected-row > * {
        background-color: #0d3149;
        color: #fff;
        width: 1%;
      }

      @media only screen and (min-width: 992px) {
        &:hover {
          .action-btns {
            opacity: 1;
            visibility: visible;
          }
        }
      }
    }
  }

  thead {
    inset-block-start: 0;
    position: relative;
    z-index: 1;
  }

  tfoot {
    inset-block-end: 0;
  }

  th,
  td {
    padding: 0px 20px;

    hr {
      height: 5px;
      background-image: url("../../../assets/images/tableDivider.webp");
      background-repeat: no-repeat;
      background-position: center center;
      background-size: contain;
    }

    .table-loader {
      width: 25px;
      height: 25px;
    }
  }

  .sort-btn {
    outline: none !important;
    box-shadow: none !important;
  }

  .btn-transparent {
    @include mixins.btn-transparent;

    & {
      background-color: #ad986f;
      font-size: 11px;
      width: 115px;
      height: 30px;
    }

    @media only screen and (min-width: 1400px) and (max-width: 1599px) {
      width: 100%;
    }
  }

  .table-actions {
    &.custom-dropdown {
      .dropdown {
        width: 20px !important;
        .dropdown-toggle {
          background: transparent;
          border: none;
          padding: 0;
          gap: 0;
          height: auto;

          &::after {
            display: none;
          }
        }

        .dropdown-menu {
          min-width: fit-content;
        }
      }
    }
  }
}

.table {
  &-user-card {
    gap: 20px;

    &-data {
      .name {
        font-size: 16px;
        font-weight: 700;
      }

      .email {
        font-size: 14px;
      }
    }
  }

  &-user-badge {
    border-radius: 50px;
    background: transparent;
    display: flex;
    width: 110px;
    height: 35px;
    font-size: 16px;
    font-weight: 600;
    color: #f9f9f9;
  }

  &-user-image {
    width: 50px;
    height: 50px;
  }

  .status-pill {
    width: 100px;
    background-color: #60a799;

    &.active {
      background-color: #60a799;
    }

    &.pending {
      background-color: #b04912;
      border-color: #b04912;
      color: #f9f9f9;
    }

    &.cancel,
    &.blocked {
      background-color: #b01212;
      border-color: #b01212;
      color: #f9f9f9;
    }
  }
}
