import {
  RiArticleLine,
  RiBookShelfLine,
  RiCustomerService2Line,
  RiLock2Line,
} from "@remixicon/react";
import { REPORTS_ROUTE_PATH } from "features/Reports/routePath";
import { ROUTE_PATH } from "routes";
import useUserStore from "stores/user";
import useFeatureFlags from "./useFeatureFlags";
import useFeatureEnable from "./useFeatureEnable";

const useSidebarLinks = () => {
  const userData = useUserStore((state) => state.userInfo.user);
  const { isFeatureEnabled } = useFeatureFlags();
  const { isReportsEnabled } = useFeatureEnable();

  const sidebarLinks = [
    {
      path: userData?.is_subscription
        ? ROUTE_PATH.HOME
        : ROUTE_PATH.SUBSCRIPTIONS,
      label: "Secure Chat",
      icon: RiLock2Line,
    },
    isFeatureEnabled("REPORTS") && isReportsEnabled
      ? {
          path: REPORTS_ROUTE_PATH.BUILD_REPORTS,
          label: "Reports",
          icon: RiArticleLine,
        }
      : null,
    isFeatureEnabled("KNOWLEDGE_BASE")
      ? {
          path: "",
          label: "Knowledge Database",
          icon: RiBookShelfLine,
          coming_soon: true,
        }
      : null,
    {
      path: ROUTE_PATH.CONTACT_US,
      label: "Contact Us",
      icon: RiCustomerService2Line,
    },
  ].filter(Boolean);
  return [sidebarLinks];
};

export default useSidebarLinks;
