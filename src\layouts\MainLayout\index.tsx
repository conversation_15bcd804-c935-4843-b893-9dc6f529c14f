import { Header, Sidebar } from "components";
import { Outlet } from "react-router-dom";
import "./styles.scss";

const MainLayout = () => {
  return (
    <div className="main-layout d-flex flex-lg-row flex-column">
      <div className="main-layout-left d-lg-block d-none">
        <Sidebar />
      </div>

      <div className="main-layout-right d-flex flex-column">
        <Header />
        <Outlet />
      </div>
    </div>
  );
};

export default MainLayout;
