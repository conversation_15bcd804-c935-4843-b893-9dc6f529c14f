import { ErrorMessage } from "formik";
import { useDebounce } from "hooks";
import React, { useState } from "react";
import { Dropdown, DropdownButton, Form } from "react-bootstrap";
import "./styles.scss";

export interface DropdownItem {
  label: string | React.ReactNode;
  value: string;
  disabled?: boolean;
}

export interface CustomDropdownProps {
  label?: string;
  title?: string | React.ReactNode;
  items: DropdownItem[];
  onSelect?: (value: string) => void;
  onSearch?: (query: string) => void;
  variant?: string;
  className?: string;
  defaultSelected?: DropdownItem;
  name?: string;
  value?: string;
  preserveTitle?: boolean;
  searchEnabled?: boolean;
  hoverTitle?: string;
}

const CustomDropdown: React.FC<CustomDropdownProps> = ({
  label,
  title = "Select an option",
  items,
  onSelect,
  onSearch,
  variant = "outline-secondary",
  className = "",
  defaultSelected,
  name,
  value,
  preserveTitle = false,
  searchEnabled = false,
  hoverTitle = "",
}) => {
  const [selected, setSelected] = useState<DropdownItem | null>(
    defaultSelected || null
  );
  const [searchQuery, setSearchQuery] = useState<string>("");
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const computedSelected = value
    ? items.find((item) => item.value === value)
    : selected;

  const handleSelect = (eventKey: string | null) => {
    const selectedItem = items.find((item) => item.value === eventKey);
    if (selectedItem) {
      if (value === undefined) {
        setSelected(selectedItem);
      }
      onSelect && onSelect(selectedItem.value);
      if (searchEnabled) {
        setSearchQuery("");
      }
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  React.useEffect(() => {
    if (searchEnabled && onSearch) {
      onSearch(debouncedSearchQuery);
    }
  }, [debouncedSearchQuery, searchEnabled, onSearch]);

  const filteredItems = searchEnabled
    ? items.filter((item) =>
        typeof item.label === "string"
          ? item.label.toLowerCase().includes(searchQuery.toLowerCase())
          : true
      )
    : items;

  return (
    <div
      className={`custom-dropdown form-group m-0 p-0 position-relative ${className}`}
      title={hoverTitle}
    >
      {label && (
        <div className="d-flex justify-content-between align-items-center">
          <label className="form-label">{label}</label>
        </div>
      )}
      <DropdownButton
        id="custom-dropdown"
        title={
          preserveTitle
            ? title
            : computedSelected
              ? computedSelected.label
              : title
        }
        variant={variant}
        className="w-100"
        onSelect={handleSelect}
      >
        {searchEnabled && (
          <div className="px-3 py-2">
            <Form.Control
              name="keyword"
              type="text"
              placeholder="Search..."
              value={searchQuery}
              onChange={handleSearch}
              autoFocus
              className="mb-2 h-100 py-2 rounded border-0"
            />
          </div>
        )}
        {filteredItems.length > 0 ? (
          filteredItems.map((item) => (
            <Dropdown.Item
              key={item.value}
              eventKey={item.value}
              disabled={item?.disabled}
            >
              {item.label}
            </Dropdown.Item>
          ))
        ) : (
          <Dropdown.Item disabled>No results found</Dropdown.Item>
        )}
      </DropdownButton>
      {name && <ErrorMessage component={"span"} name={name} />}
    </div>
  );
};

export default CustomDropdown;
