import { RiAiGenerate } from "@remixicon/react";
// TODO: Uncomment when API is ready
// import { useRegenerateSection } from "features/Reports/api";
import useReportStore from "features/Reports/store/report";
import { useState } from "react";
import { <PERSON><PERSON>, Spinner } from "react-bootstrap";
import { setConfirmModalConfig } from "stores";
// TODO: Uncomment when API is ready
// import { getTimezone } from "utils";

interface RegenerateButtonProps {
  section_id: number;
  source_section_id: number | undefined;
  index: number;
}

// Function to update section by index (more reliable than section_id)
const updateReportBlockByIndex = (index: number, content: any) => {
  const reportInfo = useReportStore.getState().reportInfo;

  if (index >= 0 && index < reportInfo.sections.length) {
    const newSections = [...reportInfo.sections];
    newSections[index] = {
      ...newSections[index],
      content: content,
    };

    useReportStore.setState((state: any) => ({
      ...state,
      isDirty: true,
      reportInfo: {
        ...state.reportInfo,
        sections: newSections,
      },
    }));
  }
};

const RegenerateButton = ({
  section_id,
  source_section_id,
  index,
}: RegenerateButtonProps) => {
  // TODO: Uncomment when API is ready
  // const { mutateAsync: regenerateSection } = useRegenerateSection();
  const [isLoading, setIsLoading] = useState(false);

  const handleRegenerateConfirm = () => {
    setIsLoading(true);

    // Immediately update the modal to show loading state
    // Use setTimeout to ensure this runs after the modal's handleClose
    setTimeout(() => {
      setConfirmModalConfig({
        visible: true,
        data: {
          icon: () => <Spinner animation="border" />,
          iconColor: "#ad986f",
          content: {
            heading: "Regenerating...",
            description: "Please wait while we regenerate the section content.",
          },
          showCloseIcon: false,
          buttonText: "Regenerating...",
          onSubmit: () => {}, // Disable button during loading
        },
      });

      // Start the actual regeneration process
      handleRegenerate();
    }, 50);
  };

  const handleRegenerate = async () => {
    // TODO: Uncomment when API is ready
    // const payload = {
    //   section_id,
    //   source_section_id,
    //   timezone: getTimezone(),
    // };

    try {
      // TODO: Uncomment when API is ready
      // const response: any = await regenerateSection(payload);

      // Mock response for now
      const response = {
        data: {
          content: {
            content: "hello world",
            type: "html",
          },
          section_id: 1551,
        },
        message: "Section generated successfully",
        success: true,
      };

      if (response?.success) {
        // Update the section content in Zustand store using index
        updateReportBlockByIndex(index, response.data.content);

        // Close the modal after a brief delay to show success
        setTimeout(() => {
          setConfirmModalConfig({
            visible: false,
            data: {},
          });
        }, 1000);
      }
    } catch (err) {
      console.log(err);
      // Close modal on error
      setConfirmModalConfig({
        visible: false,
        data: {},
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onClickRegenerate = () => {
    if (!source_section_id || isLoading) return;

    // Show initial confirmation modal
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: handleRegenerateConfirm,
        icon: RiAiGenerate,
        iconColor: "#ad986f",
        content: {
          heading: "Regenerate?",
          description: "Are you sure you want to regenerate this section?",
        },
        buttonText: "Regenerate",
      },
    });
  };
  return (
    <Button
      variant=""
      onClick={onClickRegenerate}
      title={source_section_id ? "Regenerate section" : "Not available"}
      className={`p-0 px-1 ${!source_section_id || isLoading ? "opacity-50" : ""}`}
      disabled={isLoading}
    >
      <RiAiGenerate color="#ad986f" />
    </Button>
  );
};

export default RegenerateButton;
