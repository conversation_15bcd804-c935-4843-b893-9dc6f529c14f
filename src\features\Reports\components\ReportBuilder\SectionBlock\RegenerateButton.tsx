import { RiAiGenerate } from "@remixicon/react";
import { useRegenerateSection } from "features/Reports/api";
import { Button } from "react-bootstrap";
import { setConfirmModalConfig } from "stores";
import { getTimezone } from "utils";

interface RegenerateButtonProps {
  section_id: number;
  source_section_id: number | undefined;
}

const RegenerateButton = ({
  section_id,
  source_section_id,
}: RegenerateButtonProps) => {
  const { mutateAsync: regenerateSection } = useRegenerateSection();

  const handleRegenerate = async () => {
    const payload = {
      section_id,
      source_section_id,
      timezone: getTimezone(),
    };
    try {
      //   const response: any = await regenerateSection(payload);
      const response = {
        data: {
          content: {
            content:
              "<ul><li>Appendix A: Risk Tolerance Questionnaire Results</li><li>Appendix B: Investment Policy Statement</li><li>Appendix C: Fund Prospectuses and Key Information Documents</li><li>Appendix D: Fee Schedule and Advisory Agreement</li><li>Appendix E: Regulatory Disclosures</li></ul>",
            type: "html",
          },
          section_id: 1551,
        },
        message: "Section generated successfully",
        success: true,
      };
      if (response?.success) {
        console.log(response);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const onClickRegenerate = () => {
    if (!source_section_id) return;
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: handleRegenerate,
        icon: RiAiGenerate,
        iconColor: "#ad986f",
        content: {
          heading: "Regenerate?",
          description: "Are you sure you want to regenerate this section?",
        },
      },
    });
  };
  return (
    <Button
      variant=""
      onClick={onClickRegenerate}
      title={source_section_id ? "Regenerate section" : "Not available"}
      className={`p-0 px-1 ${!source_section_id ? "opacity-50" : ""}`}
    >
      <RiAiGenerate color="#ad986f" />
    </Button>
  );
};

export default RegenerateButton;
