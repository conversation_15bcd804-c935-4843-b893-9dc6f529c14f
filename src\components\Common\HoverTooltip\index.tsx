import parse from "html-react-parser";
import { OverlayTrigger, Tooltip } from "react-bootstrap";
import "./styles.scss";

interface HoverTooltipProps {
  children: React.JSX.Element;
  title: string;
  description?: string;
  customClass?: string;
  position?: "top" | "bottom" | "left" | "right";
  isDisabled?: boolean;
}

const HoverTooltip = ({
  children,
  title,
  description,
  customClass,
  position = "bottom",
  isDisabled = false,
}: HoverTooltipProps) => {
  if (isDisabled) return <>{children}</>;
  const renderTooltip = (props: any) => (
    <Tooltip id="button-tooltip" className={customClass} {...props}>
      {title}
      {description && (
        <p className="mb-0 mt-4 fw-normal">{parse(description)}</p>
      )}
    </Tooltip>
  );
  return (
    <OverlayTrigger
      placement={position}
      overlay={renderTooltip}
      popperConfig={{ strategy: "fixed" }}
    >
      {children}
    </OverlayTrigger>
  );
};

export default HoverTooltip;
